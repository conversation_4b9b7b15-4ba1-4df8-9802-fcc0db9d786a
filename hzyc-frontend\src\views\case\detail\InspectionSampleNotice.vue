<template>
  <div class="inspection-sample-notice-container">
    <!-- 文档头部操作栏 -->
    <div class="document-header">
      <div class="header-left">
        <h3>鉴别检验留样告知书</h3>
      </div>
      <div class="header-right">
        <el-button
          v-if="!isEditing"
          size="small"
          type="primary"
          @click="toggleEdit"
          icon="Edit">
          编辑
        </el-button>
        <template v-else>
          <el-button
            size="small"
            type="success"
            @click="saveEdit"
            icon="Check">
            保存
          </el-button>
          <el-button
            size="small"
            type="info"
            @click="cancelEdit"
            icon="Close">
            取消
          </el-button>
        </template>
        <el-button
          size="small"
          @click="downloadOriginalFile"
          icon="Download">
          导出
        </el-button>
      </div>
    </div>

    <!-- 直接显示DOCX预览，不使用弹窗 -->
    <div class="document-preview" v-if="props.fileUrl&&!isEditing">
      <div class="preview-content" v-loading="previewLoading">
        <VueOfficeDocx
          :src="props.fileUrl"
          style="width: 100%;"
          @rendered="onDocxRendered"
          @error="onPreviewError"
        />
      </div>
    </div>

    <!-- 如果没有文件URL或不是DOCX，显示表单编辑模式 -->
    <div v-else class="document-body">
      <template v-if="isEditing">
        <div class="document-layout">

          <!-- 文档头部 -->
          <div class="document-header-section">
            <div class="document-title">
              <h2>鉴别检验留样告知书</h2>
            </div>
          </div>

          <!-- 收件人信息 -->
          <div class="content-section">
            <div class="recipient-section">
              <el-input
                v-model="formData.recipient"
                placeholder="收件人"
                style="width: 200px; display: inline-block;"
              />
              <span>：</span>
            </div>
          </div>

          <!-- 正文内容 -->
          <div class="content-section">
            <div class="content-paragraph">
              <span>根据《烟草专卖品鉴别检验管理办法》（国烟科〔2014〕285号）第二十五条规定，对以下鉴别检验卷烟样品进行留样，留样期限为3个月。</span>
            </div>
          </div>

          <!-- 样品清单表格 -->
          <div class="content-section">
            <div class="sample-table-container">
              <table class="sample-table">
                <thead>
                  <tr>
                    <th class="header-cell">品种</th>
                    <th class="header-cell">规格</th>
                    <th class="header-cell">数量（单位：<el-input v-model="formData.sample_unit" style="width: 60px; display: inline-block;" />）</th>
                    <th class="header-cell">备注</th>
                    <th class="add-btn-cell">
                      <el-button
                        type="primary"
                        size="small"
                        icon="Plus"
                        @click="addSampleRow"
                        circle
                      />
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <!-- 样品数据行 -->
                  <tr v-for="(sample, index) in formData.sampleList" :key="index">
                    <td class="data-cell">
                      <el-input v-model="sample.variety" size="small" placeholder="品种" />
                    </td>
                    <td class="data-cell">
                      <el-input v-model="sample.specification" size="small" placeholder="规格" />
                    </td>
                    <td class="data-cell">
                      <el-input v-model="sample.quantity" size="small" placeholder="数量" />
                    </td>
                    <td class="data-cell">
                      <el-input v-model="sample.remark" size="small" placeholder="备注" />
                    </td>
                    <td class="delete-btn-cell">
                      <el-button
                        type="danger"
                        size="small"
                        icon="Minus"
                        @click="removeSampleRow(index)"
                        circle
                        :disabled="formData.sampleList.length <= 1"
                      />
                    </td>
                  </tr>

                  <!-- 合计行 -->
                  <tr class="total-row">
                    <td class="total-cell" colspan="2">合计</td>
                    <td class="total-cell">
                      <el-input v-model="formData.total_sample_quantity" size="small" placeholder="合计数量" />
                    </td>
                    <td class="total-cell">
                      <el-input v-model="formData.total_sample_remark" size="small" placeholder="合计备注" />
                    </td>
                    <td class="total-cell"></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 承办人签名区域 -->
          <div class="content-section">
            <div class="signature-section">
              <div class="signature-row">
                <span class="signature-label">承办人（签名）：</span>
                <el-input v-model="formData.undertaker1_name" size="small" style="width: 120px;" placeholder="承办人1" />
                <span class="signature-label" style="margin-left: 30px;">执法证号：</span>
                <el-input v-model="formData.undertaker1_license" size="small" style="width: 120px;" placeholder="执法证号" />
                <el-input v-model="formData.undertaker1_year" size="small" style="width: 60px; margin-left: 20px;" placeholder="年" />
                <span>年</span>
                <el-input v-model="formData.undertaker1_month" size="small" style="width: 50px;" placeholder="月" />
                <span>月</span>
                <el-input v-model="formData.undertaker1_day" size="small" style="width: 50px;" placeholder="日" />
                <span>日</span>
              </div>

              <div class="signature-row">
                <span class="signature-label" style="margin-left: 120px;"></span>
                <el-input v-model="formData.undertaker2_name" size="small" style="width: 120px;" placeholder="承办人2" />
                <span class="signature-label" style="margin-left: 30px;">执法证号：</span>
                <el-input v-model="formData.undertaker2_license" size="small" style="width: 120px;" placeholder="执法证号" />
                <el-input v-model="formData.undertaker2_year" size="small" style="width: 60px; margin-left: 20px;" placeholder="年" />
                <span>年</span>
                <el-input v-model="formData.undertaker2_month" size="small" style="width: 50px;" placeholder="月" />
                <span>月</span>
                <el-input v-model="formData.undertaker2_day" size="small" style="width: 50px;" placeholder="日" />
                <span>日</span>
              </div>
            </div>
          </div>

          <!-- 当事人签名区域 -->
          <div class="content-section">
            <div class="signature-section">
              <div class="signature-row party-signature">
                <span class="signature-label">当事人签署意见并签名：</span>
                <el-input
                  v-model="formData.party_opinion"
                  size="small"
                  style="width: 300px;"
                  placeholder="请输入当事人意见"
                />
              </div>

              <div class="signature-row">
                <span style="margin-left: 200px;"></span>
                <el-input v-model="formData.party_signature_year" size="small" style="width: 60px;" placeholder="年" />
                <span>年</span>
                <el-input v-model="formData.party_signature_month" size="small" style="width: 50px;" placeholder="月" />
                <span>月</span>
                <el-input v-model="formData.party_signature_day" size="small" style="width: 50px;" placeholder="日" />
                <span>日</span>
              </div>
            </div>
          </div>

          <!-- 见证人签名区域 -->
          <div class="content-section">
            <div class="signature-section">
              <div class="signature-row">
                <span class="signature-label">见证人（签名）：</span>
                <el-input v-model="formData.witness1_name" size="small" style="width: 120px;" placeholder="见证人1" />
                <span>、</span>
                <el-input v-model="formData.witness2_name" size="small" style="width: 120px;" placeholder="见证人2" />
                <el-input v-model="formData.witness_year" size="small" style="width: 60px; margin-left: 20px;" placeholder="年" />
                <span>年</span>
                <el-input v-model="formData.witness_month" size="small" style="width: 50px;" placeholder="月" />
                <span>月</span>
                <el-input v-model="formData.witness_day" size="small" style="width: 50px;" placeholder="日" />
                <span>日</span>
              </div>
            </div>
          </div>

          <!-- 送达信息 -->
          <div class="content-section">
            <div class="delivery-section">
              <div class="delivery-row">
                <span class="delivery-label">送达方式：</span>
                <el-input v-model="formData.delivery_method" size="small" style="width: 150px;" placeholder="送达方式" />
                <span class="delivery-label" style="margin-left: 30px;">送达地点：</span>
                <el-input v-model="formData.delivery_location" size="small" style="width: 200px;" placeholder="送达地点" />
              </div>
            </div>
          </div>

          <!-- 落款区域 -->
          <div class="signature-section">
            <div class="signature-line">
              <el-input v-model="formData.org_seal" placeholder="落款（印章）" style="width: 300px;" />
            </div>
            <div class="date-line">
              <el-input v-model="formData.document_year" size="small" style="width: 60px;" placeholder="年" />
              <span>年</span>
              <el-input v-model="formData.document_month" size="small" style="width: 50px;" placeholder="月" />
              <span>月</span>
              <el-input v-model="formData.document_day" size="small" style="width: 50px;" placeholder="日" />
              <span>日</span>
            </div>
          </div>
        </div>
      </template>

    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Edit, Check, View, Download, FullScreen, ScaleToOriginal, Close, Plus, Minus } from '@element-plus/icons-vue'
import VueOfficeDocx from '@vue-office/docx'
// 在组件中导入样式
import '@/styles/vue-office-docx.css'
import '@/styles/document-common.scss';

const props = defineProps({
  documentData: {
    type: Object,
    default: () => ({})
  },
  fileUrl: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: ''
  }
})

// 添加调试日志
watch(() => props.fileUrl, (newVal, oldVal) => {
  console.log('fileUrl changed:', { newVal, oldVal })
}, { immediate: true })

watch(() => props, (newVal) => {
  console.log('所有props:', newVal)
}, { immediate: true, deep: true })

const emit = defineEmits(['save'])

// 编辑状态
const isEditing = ref(false)

// 表单数据
const formData = ref({
  recipient: '',
  sample_unit: '条',
  sampleList: [
    {
      variety: '',
      specification: '',
      quantity: '',
      remark: ''
    }
  ],
  total_sample_quantity: '',
  total_sample_remark: '',
  undertaker1_name: '',
  undertaker1_license: '',
  undertaker1_year: '',
  undertaker1_month: '',
  undertaker1_day: '',
  undertaker2_name: '',
  undertaker2_license: '',
  undertaker2_year: '',
  undertaker2_month: '',
  undertaker2_day: '',
  party_opinion: '',
  party_signature_year: '',
  party_signature_month: '',
  party_signature_day: '',
  witness1_name: '',
  witness2_name: '',
  witness_year: '',
  witness_month: '',
  witness_day: '',
  delivery_method: '',
  delivery_location: '',
  org_seal: '',
  document_year: '',
  document_month: '',
  document_day: ''
})

// 预览相关状态
const previewDialogVisible = ref(false)
const previewLoading = ref(false)
const isFullscreen = ref(false)

// 保存原始数据的备份
const originalFormData = ref({})

// 监听传入的文档数据
watch(() => props.documentData, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    // 从documentContent中提取属性，先解析JSON字符串
    let docContent = {}
    try {
      if (typeof newVal.documentContent === 'string') {
        docContent = JSON.parse(newVal.documentContent)
      } else {
        docContent = newVal.documentContent || {}
      }
    } catch (error) {
      docContent = {}
    }

    // 合并数据，优先使用documentContent中的数据
    formData.value = {
      ...formData.value,
      ...newVal,
      // 从documentContent中提取具体字段
      recipient: docContent.recipient || newVal.recipient || '',
      sample_unit: docContent.sample_unit || newVal.sample_unit || '条',
      sampleList: docContent.sampleList || newVal.sampleList || formData.value.sampleList,
      total_sample_quantity: docContent.total_sample_quantity || newVal.total_sample_quantity || '',
      total_sample_remark: docContent.total_sample_remark || newVal.total_sample_remark || '',
      undertaker1_name: docContent.undertaker1_name || newVal.undertaker1_name || '',
      undertaker1_license: docContent.undertaker1_license || newVal.undertaker1_license || '',
      undertaker1_year: docContent.undertaker1_year || newVal.undertaker1_year || '',
      undertaker1_month: docContent.undertaker1_month || newVal.undertaker1_month || '',
      undertaker1_day: docContent.undertaker1_day || newVal.undertaker1_day || '',
      undertaker2_name: docContent.undertaker2_name || newVal.undertaker2_name || '',
      undertaker2_license: docContent.undertaker2_license || newVal.undertaker2_license || '',
      undertaker2_year: docContent.undertaker2_year || newVal.undertaker2_year || '',
      undertaker2_month: docContent.undertaker2_month || newVal.undertaker2_month || '',
      undertaker2_day: docContent.undertaker2_day || newVal.undertaker2_day || '',
      party_opinion: docContent.party_opinion || newVal.party_opinion || '',
      party_signature_year: docContent.party_signature_year || newVal.party_signature_year || '',
      party_signature_month: docContent.party_signature_month || newVal.party_signature_month || '',
      party_signature_day: docContent.party_signature_day || newVal.party_signature_day || '',
      witness1_name: docContent.witness1_name || newVal.witness1_name || '',
      witness2_name: docContent.witness2_name || newVal.witness2_name || '',
      witness_year: docContent.witness_year || newVal.witness_year || '',
      witness_month: docContent.witness_month || newVal.witness_month || '',
      witness_day: docContent.witness_day || newVal.witness_day || '',
      delivery_method: docContent.delivery_method || newVal.delivery_method || '',
      delivery_location: docContent.delivery_location || newVal.delivery_location || '',
      org_seal: docContent.org_seal || newVal.org_seal || '',
      document_year: docContent.document_year || newVal.document_year || '',
      document_month: docContent.document_month || newVal.document_month || '',
      document_day: docContent.document_day || newVal.document_day || ''
    }

    // 保存原始数据的备份
    originalFormData.value = { ...formData.value }
  }
}, { immediate: true, deep: true })

// 修改切换编辑状态函数
const toggleEdit = () => {
  if (!isEditing.value) {
    // 进入编辑模式时保存当前数据作为备份
    originalFormData.value = { ...formData.value }
  }
  isEditing.value = !isEditing.value
}

// 新增保存编辑函数
const saveEdit = () => {
  // 保存时只传递需要的数据字段，避免传递整个formData
  const saveData = {
    recipient: formData.value.recipient,
    sample_unit: formData.value.sample_unit,
    sampleList: formData.value.sampleList,
    total_sample_quantity: formData.value.total_sample_quantity,
    total_sample_remark: formData.value.total_sample_remark,
    undertaker1_name: formData.value.undertaker1_name,
    undertaker1_license: formData.value.undertaker1_license,
    undertaker1_year: formData.value.undertaker1_year,
    undertaker1_month: formData.value.undertaker1_month,
    undertaker1_day: formData.value.undertaker1_day,
    undertaker2_name: formData.value.undertaker2_name,
    undertaker2_license: formData.value.undertaker2_license,
    undertaker2_year: formData.value.undertaker2_year,
    undertaker2_month: formData.value.undertaker2_month,
    undertaker2_day: formData.value.undertaker2_day,
    party_opinion: formData.value.party_opinion,
    party_signature_year: formData.value.party_signature_year,
    party_signature_month: formData.value.party_signature_month,
    party_signature_day: formData.value.party_signature_day,
    witness1_name: formData.value.witness1_name,
    witness2_name: formData.value.witness2_name,
    witness_year: formData.value.witness_year,
    witness_month: formData.value.witness_month,
    witness_day: formData.value.witness_day,
    delivery_method: formData.value.delivery_method,
    delivery_location: formData.value.delivery_location,
    org_seal: formData.value.org_seal,
    document_year: formData.value.document_year,
    document_month: formData.value.document_month,
    document_day: formData.value.document_day
  }

  emit('save', saveData)
  isEditing.value = false
}

// 新增取消编辑函数
const cancelEdit = () => {
  // 恢复原始数据
  formData.value = { ...originalFormData.value }
  isEditing.value = false
  ElMessage.info('已取消编辑，数据已恢复')
}

// 新增样品行
const addSampleRow = () => {
  formData.value.sampleList.push({
    variety: '',
    specification: '',
    quantity: '',
    remark: ''
  })
}

// 删除样品行
const removeSampleRow = (index) => {
  if (formData.value.sampleList.length > 1) {
    formData.value.sampleList.splice(index, 1)
  }
}

// 文档渲染完成回调
const onDocxRendered = () => {
  previewLoading.value = false
}

// 预览错误处理
const onPreviewError = (error) => {
  previewLoading.value = false
  console.error('文档预览失败:', error)
  ElMessage.error('文档预览失败，请检查文件格式或网络连接')
}

// 下载原文件
const downloadOriginalFile = () => {
  if (props.fileUrl) {
    const link = document.createElement('a')
    link.href = props.fileUrl
    link.download = props.fileName || '鉴别检验留样告知书'
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    ElMessage.success('开始下载原文件')
  } else {
    ElMessage.warning('原文件下载链接不存在')
  }
}

// 在 onMounted 中添加事件监听
onMounted(() => {
  // 监听优化事件
  const handleOptimizeEvent = (event) => {
    const { action } = event.detail
    // 根据不同的优化动作进入编辑模式
    if (action === 'sampleInfo' || action === 'undertakerInfo' || action === 'partyInfo') {
      isEditing.value = true
      ElMessage.info('已进入编辑模式，请完善相关信息')
    }
  }

  document.addEventListener('document-optimize', handleOptimizeEvent)

  // 组件卸载时移除监听器
  onUnmounted(() => {
    document.removeEventListener('document-optimize', handleOptimizeEvent)
  })
})
</script>

<style scoped>
/* 组件特有样式已移至 @/styles/document-common.scss */

/* 鉴别检验留样告知书特有样式 */
.sample-table-container {
  margin: 30px 0;
}

.sample-table {
  width: 100%;
  border-collapse: collapse;
  border: 2px solid #333;
}

.sample-table th,
.sample-table td {
  border: 1px solid #333;
  padding: 8px;
  vertical-align: middle;
  text-align: center;
}

.header-cell {
  background-color: #f0f0f0;
  font-weight: bold;
  padding: 12px 8px;
}

.data-cell {
  padding: 8px;
}

.total-row {
  background-color: #f9f9f9;
  font-weight: bold;
}

.total-cell {
  padding: 8px;
  font-weight: bold;
}

.add-btn-cell,
.delete-btn-cell {
  width: 50px;
  padding: 8px;
}

.signature-section {
  margin-top: 30px;
}

.signature-row {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  line-height: 2;
}

.signature-label {
  font-weight: 500;
  margin-right: 10px;
  white-space: nowrap;
}

.party-signature {
  margin-top: 30px;
}

.delivery-section {
  margin: 30px 0;
}

.delivery-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.delivery-label {
  font-weight: 500;
  margin-right: 10px;
  white-space: nowrap;
}

/* 确保输入框在表格中的样式 */
.sample-table .el-input {
  width: 100%;
}

.sample-table .el-input__wrapper {
  border: none;
  box-shadow: none;
  background: transparent;
}

.sample-table .el-input__inner {
  text-align: center;
}

/* 打印样式 */
@media print {
  .add-btn-cell,
  .delete-btn-cell {
    display: none;
  }
}
</style>
