package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import cn.hutool.json.JSONArray;

/**
 * 鉴别检验留样告知书文档生成实现类
 *
 * <AUTHOR>
 */
@Service("inspectionSampleNoticeDocument")
public class InspectionSampleNoticeDocumentImpl implements DocumentGenerator {

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Autowired
    private IDocumentService documentService;
    @Autowired
    private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，根据环境判断数据类型
        System.out.println(caseId);
        // dev环境使用模拟数据(type=0)，prod环境使用真实数据(type=1)
        int dataType = "prod".equals(activeProfile) ? 1 : 0;
        processedData = getMockData(dataType, caseId);
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "14鉴别检验留样告知书.docx";
    }

    @Override
    public String getDocumentType() {
        return "INSPECTION-SAMPLE-NOTICE";
    }

    public static Map<String, String> getReverseFieldMapping() {
        Map<String, String> mapping = new HashMap<>();
        
        mapping.put("CBR", "undertaker");
        mapping.put("KZZD1", "ext1");
        mapping.put("KZZD2", "ext2");
        mapping.put("AWH", "case_no");
        mapping.put("JGJC", "org_short_name");
        mapping.put("AY", "case_reason");
        mapping.put("CJR", "creator");
        mapping.put("CJSJ", "create_time");
        mapping.put("SJSSDWYYTYSJQXCL", "own_org_uuid");
        mapping.put("ZJBGWH", "inspection_report_no");
        mapping.put("FYSPBS", "fee_approval_flag");
        mapping.put("BZ", "remark");
        mapping.put("KZZD3", "ext3");
        mapping.put("AJBS", "case_uuid");
        mapping.put("XGR", "modifier");
        mapping.put("WSBHQ", "full_doc_no");
        mapping.put("SJMC", "city_name");
        mapping.put("CBRUUIDS", "undertaker_uuids");
        mapping.put("CBYJQSRQ", "undertaker_opinion_date");
        mapping.put("CBRYJ", "undertaker_opinion");
        mapping.put("ZJEDX", "total_amount_upper");
        mapping.put("AFSJ", "case_date");
        mapping.put("SJSSBMYYTYSJQXCL", "own_dept_uuid");
        mapping.put("WCZT", "complete_status");
        mapping.put("ZJE", "total_amount");
        mapping.put("DSR", "party");
        mapping.put("MCRKSJ", "mc_create_time");
        mapping.put("SJBM", "city_code");
        mapping.put("WSBH", "doc_no");
        mapping.put("XGSJ", "modify_time");
        mapping.put("SFYX", "is_active");

        return mapping;
    }

    /**
     * 获取模拟数据
     * @param type 数据类型：0-模拟数据(dev环境)，1-真实数据(prod环境)
     * @param caseId 案件ID
     */
    private Map<String, Object> getMockData(int type, String caseId) {
        Map<String, Object> mockData = new HashMap<>();
        
        if (type == 1) {
            // 生产环境：尝试获取真实数据
            Map<String, Object> query = new HashMap<>();
            query.put("AJBS", caseId);
            JSONArray array = icaseInfoService.getInspectionSampleNoticeDailyReport(query);

            // 如果array不为空，处理数据
            if (array != null && array.size() > 0) {
                return processRealData(array);
            }
        }

        // 基础信息
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("org_short_name", "广东省博罗县烟草专卖局");
        mockData.put("full_doc_no", "博烟检留﹝2025﹞第15号");
        mockData.put("doc_no", "15");
        mockData.put("case_no", "博烟案﹝2025﹞第48号");
        mockData.put("city_name", "惠州市");
        mockData.put("city_code", "10441300");

        // 收件人信息（模板中的"："前面的内容）
        mockData.put("recipient", "梁俊强");

        // 当事人信息
        mockData.put("party", "梁俊强");
        mockData.put("party_info", "当事人：梁俊强，字号：博罗县龙溪隆胜轩茶烟酒商行，性别：男性，民族：汉族，职业：个体工商户，身份证住址：广东省博罗县龙溪街道长湖村合湖小组193号，居民身份证号码：441322199203166034，烟草专卖零售许可证号：************，统一社会信用代码：92441322MA56B9KR4D，经营地址：广东省博罗县龙溪街道宫庭村龙桥大道1239号，联系电话：13640736270。");

        // 案件信息
        mockData.put("case_date", "2025/3/18");
        mockData.put("case_reason", "未在当地烟草专卖批发企业进货");

        // 样品表格数据 - 单位
        mockData.put("sample_unit", "条");

        // 创建样品列表
        List<Map<String, Object>> sampleList = new ArrayList<>();

        // 样品1
        Map<String, Object> sample1 = new HashMap<>();
        sample1.put("variety", "黄果树(长征)");
        sample1.put("specification", "20支/包，10包/条");
        sample1.put("quantity", "200");
        sample1.put("remark", "包装完好");
        sampleList.add(sample1);

        // 样品2
        Map<String, Object> sample2 = new HashMap<>();
        sample2.put("variety", "白沙(硬精品三代)");
        sample2.put("specification", "20支/包，10包/条");
        sample2.put("quantity", "150");
        sample2.put("remark", "包装完好");
        sampleList.add(sample2);

        // 样品3
        Map<String, Object> sample3 = new HashMap<>();
        sample3.put("variety", "红塔山(硬经典)");
        sample3.put("specification", "20支/包，10包/条");
        sample3.put("quantity", "150");
        sample3.put("remark", "包装完好");
        sampleList.add(sample3);

        // 样品4
        Map<String, Object> sample4 = new HashMap<>();
        sample4.put("variety", "黄山(新一品)");
        sample4.put("specification", "20支/包，10包/条");
        sample4.put("quantity", "100");
        sample4.put("remark", "包装完好");
        sampleList.add(sample4);

        // 样品5
        Map<String, Object> sample5 = new HashMap<>();
        sample5.put("variety", "娇子(蓝时代)");
        sample5.put("specification", "20支/包，10包/条");
        sample5.put("quantity", "100");
        sample5.put("remark", "包装完好");
        sampleList.add(sample5);

        mockData.put("sampleList", sampleList);

        // 合计数量
        mockData.put("total_sample_quantity", "1075");
        mockData.put("total_sample_remark", "共17个品种");

        // 留样详细信息
        mockData.put("retention_period", "3个月");
        mockData.put("retention_start_date", "2025/3/18");
        mockData.put("retention_end_date", "2025/6/18");
        mockData.put("storage_location", "广东省博罗县烟草专卖局证据保管室");
        mockData.put("storage_condition", "常温、干燥、避光保存");

        // 检验机构信息
        mockData.put("inspection_org", "广东省烟草质量监督检测站");
        mockData.put("inspection_report_no", "粤烟检﹝2025﹞第0318号");
        mockData.put("inspection_contact", "020-12345678");

        // 承办人信息
        mockData.put("undertaker1_name", "叶辉明");
        mockData.put("undertaker1_license", "19090352015");
        mockData.put("undertaker1_year", "2025");
        mockData.put("undertaker1_month", "3");
        mockData.put("undertaker1_day", "18");

        mockData.put("undertaker2_name", "朱兆强");
        mockData.put("undertaker2_license", "19090352023");
        mockData.put("undertaker2_year", "2025");
        mockData.put("undertaker2_month", "3");
        mockData.put("undertaker2_day", "18");

        // 当事人签署意见
        mockData.put("party_opinion", "同意留样，对留样过程无异议");
        mockData.put("party_signature_year", "2025");
        mockData.put("party_signature_month", "3");
        mockData.put("party_signature_day", "18");

        // 见证人信息
        mockData.put("witness1_name", "张见证");
        mockData.put("witness2_name", "李见证");
        mockData.put("witness_year", "2025");
        mockData.put("witness_month", "3");
        mockData.put("witness_day", "18");

        // 送达信息
        mockData.put("delivery_method", "直接送达");
        mockData.put("delivery_location", "当事人经营场所");

        // 落款信息
        mockData.put("org_seal", "广东省博罗县烟草专卖局");
        mockData.put("document_year", "2025");
        mockData.put("document_month", "3");
        mockData.put("document_day", "18");

        // 法律依据
        mockData.put("legal_basis", "《烟草专卖品鉴别检验管理办法》（国烟科〔2014〕285号）第二十五条");

        // 告知内容
        mockData.put("notice_content", "根据《烟草专卖品鉴别检验管理办法》（国烟科〔2014〕285号）第二十五条规定，对以下鉴别检验卷烟样品进行留样，留样期限为3个月。");

        // 系统字段
        mockData.put("is_active", 1);
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025/3/18 14:30");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025/3/18 14:30");
        mockData.put("own_dept_uuid", "4413231030000002829");
        mockData.put("own_org_uuid", "4413231030000000540");
        mockData.put("mc_create_time", "2025/3/18 14:30");
        mockData.put("complete_status", 1);

        // 费用相关
        mockData.put("fee_approval_flag", "1");
        mockData.put("total_amount", 0.00);
        mockData.put("total_amount_upper", "零元整");

        // 扩展字段
        mockData.put("ext1", "");
        mockData.put("ext2", "");
        mockData.put("ext3", "");
        mockData.put("remark", "留样样品已密封保存，当事人对留样过程无异议");

        return mockData;
    }

    /**
     * 处理真实数据
     */
    private Map<String, Object> processRealData(JSONArray array) {
        Map<String, Object> data = new HashMap<>();
        Map<String, String> mapper = getReverseFieldMapping();

        // 处理第一条记录的基础信息
        Map<String, Object> firstData = (Map<String, Object>) array.get(0);
        if (firstData != null) {
            firstData.forEach((key, value) -> {
                String newKey = mapper.get(key);
                if (StrUtil.isBlank(newKey)) {
                    newKey = key;
                }
                // 跳过样品相关字段，这些将在sampleList中处理
                if (!isSampleField(key)) {
                    data.put(newKey, value);
                }
            });
        }

        // 处理样品列表数据
        List<Map<String, Object>> sampleList = new ArrayList<>();
        for (int i = 0; i < array.size(); i++) {
            Map<String, Object> item = (Map<String, Object>) array.get(i);
            Map<String, Object> sampleItem = new HashMap<>();

            // 映射样品相关字段
            sampleItem.put("variety", item.get("WPMC") != null ? item.get("WPMC").toString() : ""); // 品种
            sampleItem.put("specification", item.get("GG") != null ? item.get("GG").toString() : ""); // 规格
            sampleItem.put("quantity", item.get("CYSL") != null ? item.get("CYSL").toString() : ""); // 数量
            sampleItem.put("remark", item.get("BZ") != null ? item.get("BZ").toString() : ""); // 备注

            sampleList.add(sampleItem);
        }

        data.put("sampleList", sampleList);

        // 计算合计数量
        int totalQuantity = sampleList.stream()
            .mapToInt(sample -> {
                try {
                    return Integer.parseInt(sample.get("quantity").toString());
                } catch (Exception e) {
                    return 0;
                }
            })
            .sum();

        data.put("total_sample_quantity", String.valueOf(totalQuantity));
        data.put("total_sample_remark", "共" + sampleList.size() + "个品种");

        return data;
    }

    /**
     * 判断是否为样品相关字段
     */
    private boolean isSampleField(String key) {
        return "WPMC".equals(key) || "GG".equals(key) || "CYSL".equals(key) || "BZ".equals(key);
    }
}
