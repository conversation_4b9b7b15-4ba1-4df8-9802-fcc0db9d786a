package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 抽样取证物品清单文档生成实现类
 *
 * <AUTHOR>
 */
@Service("samplingListDocument")
public class SamplingEvidenceListImpl implements DocumentGenerator {

    @Autowired
    private IDocumentService documentService;
	@Autowired
	private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，使用模拟数据
		processedData = StrUtil.isNotBlank(caseId) ? getData(caseId) : getMockData();
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "13抽样取证物品清单.docx";
    }

    @Override
    public String getDocumentType() {
        return "SAMPLING-EVIDENCE-LIST";
    }

	private Map<String, Object> getData(String caseId) {
		Map<String, Object> data = new HashMap<>();

		// 正式环境：使用真实接口数据
		Map<String, Object> query = new HashMap<>();
		query.put("AJBS", caseId);
		JSONArray array = icaseInfoService.getCaseSamplingItemList(query);

		// 本地调试：使用模拟数据（如需调试请取消注释）
		// JSONArray array = createMockSampleData();

		if(array != null && !array.isEmpty()) {
			Map<String, String> mapper = getTableFieldMapping();

			// 处理第一条记录的基础信息
			Map<String, Object> firstData = (Map<String, Object>) array.get(0);
			if(firstData != null) {
				firstData.forEach((key, value) -> {
					String newKey = mapper.get(key);
					if (StrUtil.isBlank(newKey)) {
						newKey = key;
					}
					// 跳过样品相关字段，这些将在sampleList中处理
					if (!isSampleField(key)) {
						// 处理时间戳格式化
						if (value instanceof Number && (newKey.contains("date") || newKey.contains("time") || "doc_date".equals(newKey))) {
							try {
								long timestamp = ((Number) value).longValue();
								java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy年MM月dd日");
								data.put(newKey, sdf.format(new java.util.Date(timestamp)));
							} catch (Exception e) {
								data.put(newKey, value);
							}
						} else {
							data.put(newKey, value);
						}
					}
				});
			}

			// 处理样品列表数据
			List<Map<String, Object>> sampleList = new ArrayList<>();
			for (int i = 0; i < array.size(); i++) {
				Map<String, Object> item = (Map<String, Object>) array.get(i);
				Map<String, Object> sampleItem = new HashMap<>();

				// 映射样品相关字段，确保所有值都不为null
				sampleItem.put("goods_name", item.get("WPMC") != null ? item.get("WPMC").toString() : ""); // 物品名称
				sampleItem.put("pack_style", item.get("BZXS") != null ? item.get("BZXS").toString() : ""); // 包装形式
				sampleItem.put("qty", item.get("YPJS") != null ? item.get("YPJS").toString() : ""); // 样品基数
				sampleItem.put("handle_qty", item.get("CYSL") != null ? item.get("CYSL").toString() : ""); // 抽样数量
				sampleItem.put("sample_content", item.get("BZ") != null ? item.get("BZ").toString() : ""); // 样品情况/备注
				sampleItem.put("spec", item.get("GG") != null ? item.get("GG").toString() : ""); // 规格
				sampleItem.put("unit", item.get("DW") != null ? item.get("DW").toString() : ""); // 单位
				sampleItem.put("order_index", item.get("XH") != null ? item.get("XH").toString() : ""); // 序号

				sampleList.add(sampleItem);
			}

			data.put("sampleList", sampleList);

			// 同时为兼容性添加前几个样品的字段（用于简单模板）
			if (!sampleList.isEmpty()) {
				Map<String, Object> firstSample = sampleList.get(0);
				data.put("goods_name", firstSample.get("goods_name"));
				data.put("pack_style", firstSample.get("pack_style"));
				data.put("qty", firstSample.get("qty"));
				data.put("handle_qty", firstSample.get("handle_qty"));
				data.put("sample_content", firstSample.get("sample_content"));

				// 添加前5个样品的单独字段
				for (int i = 0; i < Math.min(5, sampleList.size()); i++) {
					Map<String, Object> sample = sampleList.get(i);
					data.put("goods_name_" + (i + 1), sample.get("goods_name"));
					data.put("pack_style_" + (i + 1), sample.get("pack_style"));
					data.put("qty_" + (i + 1), sample.get("qty"));
					data.put("handle_qty_" + (i + 1), sample.get("handle_qty"));
					data.put("sample_content_" + (i + 1), sample.get("sample_content"));
				}
			}



			return data;
		}

		// 如果没有数据，返回空的sampleList
		data.put("sampleList", new ArrayList<>());
		return data;
	}

	/**
	 * 判断是否为样品相关字段
	 */
	private boolean isSampleField(String key) {
		return "WPMC".equals(key) || "BZXS".equals(key) || "YPJS".equals(key) ||
			   "CYSL".equals(key) || "BZ".equals(key) || "GG".equals(key) ||
			   "DW".equals(key) || "XH".equals(key);
	}

	/**
	 * 创建本地调试用的模拟数据
	 */
	private JSONArray createMockSampleData() {
		JSONArray array = new JSONArray();

		// 样品1：芙蓉王(硬)
		Map<String, Object> sample1 = new HashMap<>();
		sample1.put("CYQDMXBS", "sample1-uuid");
		sample1.put("AJMC", "张**涉嫌无烟草专卖零售许可证经营烟草制品零售业务");
		sample1.put("DSR", "张**");
		sample1.put("WPMC", "芙蓉王(硬)");
		sample1.put("BZXS", "条盒硬盒");
		sample1.put("YPJS", 1.0);
		sample1.put("CYSL", 1.0);
		sample1.put("BZ", "卷烟真假待鉴定。");
		sample1.put("GG", "84");
		sample1.put("DW", "条");
		sample1.put("XH", 1);
		sample1.put("DWJC", "XX省XX县烟草专卖局");
		sample1.put("LABH", "X烟立﹝2025﹞第XX号");
		sample1.put("CYDD", "XX省XX市XX县***（自主承诺申报）");
		sample1.put("CBR", "李*(1909***17),郑**（19***)");
		sample1.put("CYMD", "鉴别检验");
		sample1.put("CYRQ", 1746979200000L);
		sample1.put("WSRQ", 1746979200000L);
		sample1.put("AJBS", "123456123");
		array.add(sample1);

		// 样品2：真龙(起源)
		Map<String, Object> sample2 = new HashMap<>();
		sample2.put("CYQDMXBS", "sample2-uuid");
		sample2.put("AJMC", "李**涉嫌无烟草专卖零售许可证经营烟草制品零售业务");
		sample2.put("DSR", "李**");
		sample2.put("WPMC", "真龙(起源)");
		sample2.put("BZXS", "条盒硬盒");
		sample2.put("YPJS", 1.0);
		sample2.put("CYSL", 1.0);
		sample2.put("BZ", "卷烟真假待鉴定。");
		sample2.put("GG", "84");
		sample2.put("DW", "条");
		sample2.put("XH", 2);
		sample2.put("DWJC", "XX省XX县烟草专卖局");
		sample2.put("LABH", "X烟立﹝2025﹞第XX号");
		sample2.put("CYDD", "XX省XX市XX县***（自主承诺申报）");
		sample2.put("CBR", "李*(1909***17),郑**（19***)");
		sample2.put("CYMD", "鉴别检验");
		sample2.put("CYRQ", 1746979200000L);
		sample2.put("WSRQ", 1746979200000L);
		sample2.put("AJBS", "123456123");
		array.add(sample2);

		// 样品3：双喜(硬经典1906)
		Map<String, Object> sample3 = new HashMap<>();
		sample3.put("CYQDMXBS", "sample3-uuid");
		sample3.put("AJMC", "王**涉嫌无烟草专卖零售许可证经营烟草制品零售业务");
		sample3.put("DSR", "王**");
		sample3.put("WPMC", "双喜(硬经典1906)");
		sample3.put("BZXS", "条盒硬盒");
		sample3.put("YPJS", 2.0);
		sample3.put("CYSL", 1.0);
		sample3.put("BZ", "卷烟真假待鉴定。");
		sample3.put("GG", "84");
		sample3.put("DW", "条");
		sample3.put("XH", 3);
		sample3.put("DWJC", "XX省XX县烟草专卖局");
		sample3.put("LABH", "X烟立﹝2025﹞第XX号");
		sample3.put("CYDD", "XX省XX市XX县***（自主承诺申报）");
		sample3.put("CBR", "李*(1909***17),郑**（19***)");
		sample3.put("CYMD", "鉴别检验");
		sample3.put("CYRQ", 1746979200000L);
		sample3.put("WSRQ", 1746979200000L);
		sample3.put("AJBS", "123456123");
		array.add(sample3);

		return array;
	}

	public static Map<String, String> getTableFieldMapping() {
		Map<String, String> fieldMap = new HashMap<>();

		fieldMap.put("CYQDMXBS", "handle_dtl_uuid");
		fieldMap.put("AJMC", "case_name");
		fieldMap.put("DSR", "party");
		fieldMap.put("AY", "cause_of_action");
		fieldMap.put("KZZD1", "ext1");
		fieldMap.put("ND", "doc_year");
		fieldMap.put("CYQZWPQDBS", "handle_uuid");
		fieldMap.put("TZZD", "ext_json");
		fieldMap.put("AJBS", "case_uuid");
		fieldMap.put("XTCJSJCXBYDX", "sys_create_time");
		fieldMap.put("FWZXZDTBSJYFWZXSJS", "sysisdelete");
		fieldMap.put("BZXS", "pack_style");
		fieldMap.put("SJBM", "city_org_code");
		fieldMap.put("WSRQ", "doc_date");
		fieldMap.put("XGR", "modifier");
		fieldMap.put("XGSJ", "modify_time");
		fieldMap.put("CYNR", "sample_content");
		fieldMap.put("KZZD3", "ext3");
		fieldMap.put("WPUUID", "goods_uuid");
		fieldMap.put("SXXZFLDL", "prop_type");
		fieldMap.put("WPFL", "goods_type");
		fieldMap.put("CYRQ", "sample_date");
		fieldMap.put("WSHQ", "full_doc_no");
		fieldMap.put("MCRKSJ", "mc_tec_ctime");
		fieldMap.put("KZZD2", "ext2");
		fieldMap.put("XYWYBS", "tid");
		fieldMap.put("SXXZFLXL", "prop_subtype");
		fieldMap.put("CLJG", "sample_result");
		fieldMap.put("WPMXBS", "goods_dtl_uuid");
		fieldMap.put("WSH", "doc_no");
		fieldMap.put("SJMC", "city_org_name");
		fieldMap.put("XTGXSJCXBYDX", "sys_modify_time");
		fieldMap.put("XH", "order_index");
		fieldMap.put("GG", "spec");
		fieldMap.put("BZ", "remark");
		fieldMap.put("FWZXZDTBSJYFWZXSJG", "sysupdatetime");
		fieldMap.put("CBRUUID", "undertaker_uuids");
		fieldMap.put("DW", "unit");
		fieldMap.put("LABH", "case_code");
		fieldMap.put("SFYX", "is_active");
		fieldMap.put("SJSSBMYYTYSJQXCL", "own_dept_uuid");
		fieldMap.put("YPJS", "qty");
		fieldMap.put("CBR", "undertaker");
		fieldMap.put("TAR", "same_party");
		fieldMap.put("CYSL", "handle_qty");
		fieldMap.put("DWJC", "org_short_name");
		fieldMap.put("CYDD", "address");
		fieldMap.put("CJR", "creator");
		fieldMap.put("DWSXZ", "org_abbr");
		fieldMap.put("WPMC", "goods_name");
		fieldMap.put("CBRZFZH", "undertaker_insp_no");
		fieldMap.put("SJSSDWYYTYSJQXCL", "own_org_uuid");
		fieldMap.put("WSZT", "finsh_status");
		fieldMap.put("CYMD", "sample_dest");
		fieldMap.put("WFGD", "legal_argument");
		fieldMap.put("WSCJSJ", "create_time");

		return fieldMap;
	}

    /**
     * 获取模拟数据
     */
    private Map<String, Object> getMockData() {
        Map<String, Object> mockData = new HashMap<>();

        // 基础信息
        mockData.put("sampling_uuid", "51f8b0721d3c43fdb0adbc151c3a1489");
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("org_shortname", "广东省博罗县烟草专卖局");
        mockData.put("full_doc_no", "博烟抽﹝2025﹞第48号");
        mockData.put("doc_date", "2025年6月10日");

        // 案件信息
        mockData.put("case_name", "梁俊强涉嫌违法经营卷烟案");
        mockData.put("case_code", "博烟案﹝2025﹞第48号");

        // 当事人信息
        mockData.put("party_name", "梁俊强");
        mockData.put("party_info", "当事人：梁俊强，字号：博罗县龙溪隆胜轩茶烟酒商行，性别：男性，民族：汉族，职业：个体工商户，身份证住址：广东省博罗县龙溪街道长湖村合湖小组193号，居民身份证号码：441322199203166034，烟草专卖零售许可证号：************，统一社会信用代码：92441322MA56B9KR4D，经营地址：广东省博罗县龙溪街道宫庭村龙桥大道1239号，联系电话：13640736270。");

        // 抽样信息
        mockData.put("sampling_time", "2025年03月18日");
        mockData.put("sampling_location", "广东省惠州市博罗县龙溪街道宫庭村龙桥大道1239号");
        mockData.put("sampling_reason", "涉嫌违法经营卷烟，需要抽样取证进行检验鉴定");
        mockData.put("undertaker", "叶辉明,朱兆强");
        mockData.put("undertaker_insp_no", "19090352015,19090352023");

        // 抽样物品清单
        List<Map<String, Object>> samplingItems = new ArrayList<>();

        Map<String, Object> item1 = new HashMap<>();
        item1.put("order_index", 1);
        item1.put("goods_name", "黄果树(长征)");
        item1.put("spec", "20支/包，10包/条");
        item1.put("unit", "条");
        item1.put("sampling_qty", 5.0);
        item1.put("total_qty", 200.0);
        item1.put("price", 100.0);
        item1.put("sampling_amt", 500.0);
        item1.put("batch_no", "20250315001");
        item1.put("production_date", "2025-03-15");
        item1.put("sampling_purpose", "检验真伪");
        item1.put("memo", "抽取样品用于检验鉴定");
        samplingItems.add(item1);

        Map<String, Object> item2 = new HashMap<>();
        item2.put("order_index", 2);
        item2.put("goods_name", "白沙(硬精品三代)");
        item2.put("spec", "20支/包，10包/条");
        item2.put("unit", "条");
        item2.put("sampling_qty", 3.0);
        item2.put("total_qty", 150.0);
        item2.put("price", 120.0);
        item2.put("sampling_amt", 360.0);
        item2.put("batch_no", "20250316002");
        item2.put("production_date", "2025-03-16");
        item2.put("sampling_purpose", "检验真伪");
        item2.put("memo", "抽取样品用于检验鉴定");
        samplingItems.add(item2);

        Map<String, Object> item3 = new HashMap<>();
        item3.put("order_index", 3);
        item3.put("goods_name", "红塔山(硬经典)");
        item3.put("spec", "20支/包，10包/条");
        item3.put("unit", "条");
        item3.put("sampling_qty", 3.0);
        item3.put("total_qty", 150.0);
        item3.put("price", 80.0);
        item3.put("sampling_amt", 240.0);
        item3.put("batch_no", "20250317003");
        item3.put("production_date", "2025-03-17");
        item3.put("sampling_purpose", "检验真伪");
        item3.put("memo", "抽取样品用于检验鉴定");
        samplingItems.add(item3);

        mockData.put("sampling_items", samplingItems);

        // 统计信息
        mockData.put("total_sampling_qty", 11.0);
        mockData.put("total_sampling_amt", 1100.0);
        mockData.put("total_goods_qty", 500.0);
        mockData.put("sampling_ratio", "2.2%");

        // 抽样方法
        mockData.put("sampling_method", "随机抽样");
        mockData.put("sampling_standard", "按照《烟草专卖品抽样检验管理办法》执行");

        // 保存方式
        mockData.put("storage_method", "密封保存");
        mockData.put("storage_location", "广东省博罗县烟草专卖局证据保管室");
        mockData.put("storage_condition", "常温、干燥、避光保存");

        // 检验机构
        mockData.put("inspection_org", "广东省烟草质量监督检测站");
        mockData.put("inspection_contact", "020-12345678");

        // 法律依据
        mockData.put("legal_basis", "《中华人民共和国行政处罚法》第三十七条、《烟草专卖行政处罚程序规定》第十九条、《烟草专卖品抽样检验管理办法》第八条");

        // 备注说明
        mockData.put("notice_content", "1. 抽样过程全程录像；2. 样品密封后由当事人签字确认；3. 检验结果将作为案件处理依据；4. 当事人对抽样过程无异议。");

        // 当事人确认
        mockData.put("party_signature", "梁俊强");
        mockData.put("party_signature_time", "2025年03月18日");
        mockData.put("party_opinion", "对抽样过程无异议");

        // 执法人员
        mockData.put("executor1_name", "叶辉明");
        mockData.put("executor1_insp_no", "19090352015");
        mockData.put("executor2_name", "朱兆强");
        mockData.put("executor2_insp_no", "19090352023");

        // 系统字段
        mockData.put("is_active", 1);
        mockData.put("creator", "蔡秋宝");
        mockData.put("create_time", "2025/6/10 17:15");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("modify_time", "2025/6/10 17:15");
        mockData.put("sysisdelete", "");
        mockData.put("sysupdatetime", "");
        mockData.put("own_dept_uuid", "4413231030000002829");
        mockData.put("own_org_uuid", "4413231030000000540");
        mockData.put("sys_create_time", "2025/6/10 17:15");
        mockData.put("sys_modify_time", "2025/6/10 17:15");

        // 扩展字段
        mockData.put("ext1", "");
        mockData.put("ext2", "");
        mockData.put("ext3", "");
        mockData.put("city_org_code", "10441300");
        mockData.put("city_org_name", "惠州市");

        return mockData;
    }
}
