package org.springblade.modules.hzyc.DocumentGeneration.service;

import org.apache.poi.xwpf.usermodel.*;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class WordDocumentService {

    private static final String TEMPLATE_PATH = "templates/word/";

    /**
     * 根据模板名称和数据生成Word文档
     * @param templateName 模板名称
     * @param data 填充数据
     * @return 生成的文件字节数组
     */
    public byte[] generateDocument(String templateName, Map<String, Object> data) throws IOException {
        // 获取模板文件
        InputStream templateStream = getTemplateInputStream(templateName);

        XWPFDocument document = null;
        try {
            // 尝试创建XWPFDocument
            document = new XWPFDocument(templateStream);

            // 替换文档中的占位符
            replaceTextInDocument(document, data);

            // 输出到字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);

            return outputStream.toByteArray();

        } catch (Exception e) {
            // 如果POI版本有问题，记录错误并抛出更明确的异常
            throw new IOException("POI文档处理失败，可能是版本冲突: " + e.getMessage(), e);
        } finally {
            // 确保资源正确关闭
            if (document != null) {
                try {
                    document.close();
                } catch (Exception e) {
                    // 忽略关闭异常
                }
            }
            if (templateStream != null) {
                try {
                    templateStream.close();
                } catch (Exception e) {
                    // 忽略关闭异常
                }
            }
        }
    }

    private void processTable(XWPFTable table, Map<String, Object> data) {
        List<XWPFTableRow> rows = table.getRows();
        if (rows.isEmpty()) return;

        // 检查整个表格是否包含动态表格标识
        boolean hasDynamicTable = false;
        String dynamicTableText = "";

        for (XWPFTableRow row : rows) {
            String rowText = getRowText(row);
            if (rowText.contains("${table:")) {
                hasDynamicTable = true;
                dynamicTableText = rowText;
                break;
            }
        }



        if (hasDynamicTable) {
            // 先处理动态表格
            processDynamicTableOnly(table, data);
            // 再处理该表中的普通占位符
            replaceSimplePlaceholdersInTable(table, data);
        } else {
            // 处理普通表格占位符
            replaceSimplePlaceholdersInTable(table, data);
        }
    }

    /**
     * 获取模板文件输入流，支持多种环境
     */
    private InputStream getTemplateInputStream(String templateName) throws IOException {
        // 尝试从classpath获取
        try {
            Resource resource = new ClassPathResource(TEMPLATE_PATH + templateName);
            if (resource.exists()) {
                return resource.getInputStream();
            }
        } catch (Exception e) {
            // 忽略异常，尝试下一种方法
        }

        // 尝试从ResourceUtils获取
        try {
            File file = ResourceUtils.getFile("classpath:" + TEMPLATE_PATH + templateName);
            if (file.exists()) {
                return new FileInputStream(file);
            }
        } catch (Exception e) {
            // 忽略异常，尝试下一种方法
        }

        // 尝试从主资源目录获取
        try {
            File file = new File("src/main/resources/" + TEMPLATE_PATH + templateName);
            if (file.exists()) {
                return new FileInputStream(file);
            }
        } catch (Exception e) {
            // 忽略异常，尝试下一种方法
        }

        // 如果所有方法都失败，抛出异常
        throw new FileNotFoundException("无法找到模板文件: " + templateName);
    }

    /**
     * 替换文档中的占位符
     */
    private void replaceTextInDocument(XWPFDocument document, Map<String, Object> data) {
        // 替换段落中的占位符
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replacePlaceholdersInParagraph(paragraph, data);
        }

        // 替换表格中的占位符
        List<XWPFTable> tables = document.getTables();
        for (XWPFTable table : tables) {
            processTable(table, data);
        }

        // 替换页眉页脚中的占位符
        for (XWPFHeader header : document.getHeaderList()) {
            for (XWPFParagraph paragraph : header.getParagraphs()) {
                replacePlaceholdersInParagraph(paragraph, data);
            }
            for (XWPFTable table : header.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            replacePlaceholdersInParagraph(paragraph, data);
                        }
                    }
                }
            }
        }

        for (XWPFFooter footer : document.getFooterList()) {
            for (XWPFParagraph paragraph : footer.getParagraphs()) {
                replacePlaceholdersInParagraph(paragraph, data);
            }
            for (XWPFTable table : footer.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            replacePlaceholdersInParagraph(paragraph, data);
                        }
                    }
                }
            }
        }
    }

    /**
     * 替换段落中的占位符
     */
    private void replacePlaceholdersInParagraph(XWPFParagraph paragraph, Map<String, Object> data) {
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs == null || runs.isEmpty()) {
            return;
        }

        // 构建完整的段落文本和Run映射
        StringBuilder fullTextBuilder = new StringBuilder();
        List<RunInfo> runInfos = new ArrayList<>();

        for (int i = 0; i < runs.size(); i++) {
            XWPFRun run = runs.get(i);
            String text = run.getText(0);
            if (text != null) {
                int startPos = fullTextBuilder.length();
                fullTextBuilder.append(text);
                int endPos = fullTextBuilder.length();
                runInfos.add(new RunInfo(i, startPos, endPos, run));
            }
        }

        String fullText = fullTextBuilder.toString();
        if (!fullText.contains("${")) {
            return;
        }

        // 查找并替换所有占位符
        Pattern pattern = Pattern.compile("\\$\\{([^}]*)\\}");
        Matcher matcher = pattern.matcher(fullText);

        // 收集所有需要替换的占位符信息
        List<PlaceholderInfo> placeholders = new ArrayList<>();
        while (matcher.find()) {
            String placeholder = matcher.group(0);
            String key = matcher.group(1);
            Object value = data.get(key);
            String replacement = (value != null) ? value.toString() : "";

            placeholders.add(new PlaceholderInfo(
                matcher.start(), matcher.end(), placeholder, replacement
            ));
        }

        if (placeholders.isEmpty()) {
            return;
        }

        // 从后往前替换，避免位置偏移问题
        java.util.Collections.reverse(placeholders);

        for (PlaceholderInfo placeholder : placeholders) {
            fullText = fullText.substring(0, placeholder.start) +
                      placeholder.replacement +
                      fullText.substring(placeholder.end);
        }

        // 重新分配文本到Run中，尽量保持原有格式
        redistributeTextToRuns(paragraph, fullText, runInfos);
    }

    /**
     * 表格内普通占位符替换（不处理动态表格，只做 ${key} 文本替换）
     */
    private void replaceSimplePlaceholdersInTable(XWPFTable table, Map<String, Object> data) {
        for (XWPFTableRow row : table.getRows()) {
            for (XWPFTableCell cell : row.getTableCells()) {
                for (XWPFParagraph p : cell.getParagraphs()) {
                    replacePlaceholdersInParagraph(p, data);
                }
            }
        }
    }

    /**
     * 替换一个单元格内所有段落的占位符
     */
    private void replacePlaceholdersInCell(XWPFTableCell cell, Map<String, Object> data) {
        for (XWPFParagraph p : cell.getParagraphs()) {
            replacePlaceholdersInParagraph(p, data);
        }
    }

    /**
     * 重新分配文本到Run中
     */
    private void redistributeTextToRuns(XWPFParagraph paragraph, String newText, List<RunInfo> runInfos) {
        // 清空所有Run的文本
        for (RunInfo runInfo : runInfos) {
            runInfo.run.setText("", 0);
        }

        if (newText.isEmpty()) {
            return;
        }

        // 如果有Run，将所有文本放到第一个Run中，保持其格式
        if (!runInfos.isEmpty()) {
            runInfos.get(0).run.setText(newText, 0);

            // 删除其他空Run（可选）
            for (int i = runInfos.size() - 1; i > 0; i--) {
                paragraph.removeRun(runInfos.get(i).runIndex);
            }
        } else {
            // 如果没有Run，创建一个新的
            XWPFRun newRun = paragraph.createRun();
            newRun.setText(newText);
        }
    }

    /**
     * Run信息类
     */
    private static class RunInfo {
        int runIndex;
        int startPos;
        int endPos;
        XWPFRun run;

        RunInfo(int runIndex, int startPos, int endPos, XWPFRun run) {
            this.runIndex = runIndex;
            this.startPos = startPos;
            this.endPos = endPos;
            this.run = run;
        }
    }

    /**
     * 占位符信息类
     */
    private static class PlaceholderInfo {
        int start;
        int end;
        String placeholder;
        String replacement;

        PlaceholderInfo(int start, int end, String placeholder, String replacement) {
            this.start = start;
            this.end = end;
            this.placeholder = placeholder;
            this.replacement = replacement;
        }
    }
    /**
 * 处理动态表格
 */
private void processDynamicTable(XWPFTable table, Map<String, Object> data) {
    List<XWPFTableRow> rows = table.getRows();
    if (rows.size() < 2) {
        System.out.println("动态表格行数不足，需要至少2行（表头+模板行）");
        return;
    }

    // 找到包含动态表格标识的行
    XWPFTableRow headerRow = null;
    XWPFTableRow templateRow = null;
    String tableKey = null;

    Pattern pattern = Pattern.compile("\\$\\{table:([^}]*)\\}");

    for (int i = 0; i < rows.size() - 1; i++) {
        String rowText = getRowText(rows.get(i));
        Matcher matcher = pattern.matcher(rowText);

        if (matcher.find()) {
            headerRow = rows.get(i);
            templateRow = rows.get(i + 1);
            tableKey = matcher.group(1);
            System.out.println("在第" + (i + 1) + "行找到动态表格标识，模板行为第" + (i + 2) + "行");
            break;
        }
    }

    if (headerRow == null || templateRow == null || tableKey == null) {
        System.out.println("未找到有效的动态表格标识和模板行");
        return;
    }

    System.out.println("找到动态表格键: " + tableKey);

    Object tableData = data.get(tableKey);
    System.out.println("表格数据: " + tableData);

    if (!(tableData instanceof List)) {
        System.out.println("表格数据不是List类型: " + (tableData != null ? tableData.getClass() : "null"));
        return;
    }

    @SuppressWarnings("unchecked")
    List<Map<String, Object>> tableList = (List<Map<String, Object>>) tableData;
    System.out.println("表格数据条数: " + tableList.size());

    // 保存模板行的文本内容（而不是对象引用）
    List<String> templateCellTexts = new ArrayList<>();
    for (XWPFTableCell cell : templateRow.getTableCells()) {
        templateCellTexts.add(cell.getText());
    }
    System.out.println("模板行单元格数量: " + templateCellTexts.size());
    System.out.println("模板行内容: " + templateCellTexts);

    // 找到模板行的索引并删除
    int templateRowIndex = -1;
    for (int i = 0; i < table.getRows().size(); i++) {
        if (table.getRows().get(i) == templateRow) {
            templateRowIndex = i;
            break;
        }
    }

    if (templateRowIndex >= 0) {
        table.removeRow(templateRowIndex);
        System.out.println("已删除第" + (templateRowIndex + 1) + "行模板行，当前表格行数: " + table.getRows().size());
    } else {
        System.out.println("警告：未找到模板行索引");
    }

    // 为每条数据创建新行
    for (int index = 0; index < tableList.size(); index++) {
        Map<String, Object> rowData = tableList.get(index);
        System.out.println("处理第" + (index + 1) + "行数据: " + rowData);

        XWPFTableRow newRow = table.createRow();
        System.out.println("创建新行，当前表格行数: " + table.getRows().size());

        // 确保新行有足够的单元格
        List<XWPFTableCell> newCells = newRow.getTableCells();
        while (newCells.size() < templateCellTexts.size()) {
            newRow.addNewTableCell();
            newCells = newRow.getTableCells();
        }

        System.out.println("新行单元格数量: " + newCells.size());

        // 填充行数据
        for (int i = 0; i < Math.min(newCells.size(), templateCellTexts.size()); i++) {
            XWPFTableCell cell = newCells.get(i);
            String templateText = templateCellTexts.get(i);

            // 复制模板单元格的样式（包括边框）
            if (i < templateRow.getTableCells().size()) {
                XWPFTableCell templateCell = templateRow.getTableCells().get(i);
                copyCellStyle(templateCell, cell);
            }

            // 直接处理文本并替换占位符
            fillCellWithTemplate(cell, templateText, rowData);
            System.out.println("填充第" + (i + 1) + "个单元格完成");
        }

        System.out.println("第" + (index + 1) + "行处理完成");
    }

    // 清理表头中的动态表格标识（只清理动态表格标识，不处理其他占位符）
    for (XWPFTableCell cell : headerRow.getTableCells()) {
        for (XWPFParagraph paragraph : cell.getParagraphs()) {
            String text = paragraph.getText();
            if (text != null && text.contains("${table:")) {
                // 只移除动态表格标识，保留其他内容
                String cleanedText = text.replaceAll("\\$\\{table:[^}]*\\}", "");

                // 清空段落并重新设置文本
                paragraph.getRuns().clear();
                XWPFRun run = paragraph.createRun();
                run.setText(cleanedText);
            }
        }
    }

    System.out.println("动态表格处理完成");
}

/**
 * 专门处理动态表格，不影响其他占位符
 */
private void processDynamicTableOnly(XWPFTable table, Map<String, Object> data) {
    List<XWPFTableRow> rows = table.getRows();
    if (rows.size() < 2) {
        return;
    }

    // 找到包含动态表格标识的行
    XWPFTableRow headerRow = null;
    XWPFTableRow templateRow = null;
    String tableKey = null;

    Pattern pattern = Pattern.compile("\\$\\{table:([^}]*)\\}");

    for (int i = 0; i < rows.size() - 1; i++) {
        String rowText = getRowText(rows.get(i));
        Matcher matcher = pattern.matcher(rowText);

        if (matcher.find()) {
            headerRow = rows.get(i);
            templateRow = rows.get(i + 1);
            tableKey = matcher.group(1);
            break;
        }
    }

    if (headerRow == null || templateRow == null || tableKey == null) {
        return;
    }

    Object tableData = data.get(tableKey);
    if (!(tableData instanceof List)) {
        return;
    }

    @SuppressWarnings("unchecked")
    List<Map<String, Object>> tableList = (List<Map<String, Object>>) tableData;

    // 保存模板行的文本内容
    List<String> templateCellTexts = new ArrayList<>();
    for (XWPFTableCell cell : templateRow.getTableCells()) {
        templateCellTexts.add(cell.getText());
    }

    // 找到模板行的索引并删除
    int templateRowIndex = -1;
    for (int i = 0; i < table.getRows().size(); i++) {
        if (table.getRows().get(i) == templateRow) {
            templateRowIndex = i;
            break;
        }
    }

    if (templateRowIndex < 0) {
        return;
    }

    // 在模板行后逐行插入新行
    int insertIndex = templateRowIndex + 1;
    for (int index = 0; index < tableList.size(); index++) {
        Map<String, Object> rowData = tableList.get(index);
        XWPFTableRow newRow = table.insertNewTableRow(insertIndex++);

        // 为新行创建单元格并填充
        for (int i = 0; i < templateCellTexts.size(); i++) {
            XWPFTableCell cell = newRow.addNewTableCell();
            String templateText = templateCellTexts.get(i);

            // 复制模板单元格的样式（包括边框）
            if (i < templateRow.getTableCells().size()) {
                XWPFTableCell templateCell = templateRow.getTableCells().get(i);
                copyCellStyle(templateCell, cell);
            }

            fillCellWithTemplate(cell, templateText, rowData);
        }
    }

    // 最后删除模板行
    table.removeRow(templateRowIndex);

    // 清理表头中的动态表格标识（只清理动态表格标识，不处理其他占位符）
    for (XWPFTableCell cell : headerRow.getTableCells()) {
        for (XWPFParagraph paragraph : cell.getParagraphs()) {
            String text = paragraph.getText();
            if (text != null && text.contains("${table:")) {
                // 只移除动态表格标识，保留其他内容
                String cleanedText = text.replaceAll("\\$\\{table:[^}]*\\}", "");

                // 安全地清空段落并重新设置文本
                clearParagraphAndSetText(paragraph, cleanedText);
            }
        }
    }


}

/**
 * 获取行文本内容
 */
private String getRowText(XWPFTableRow row) {
    StringBuilder sb = new StringBuilder();
    for (XWPFTableCell cell : row.getTableCells()) {
        sb.append(cell.getText());
    }
    return sb.toString();
}

/**
 * 复制行样式
 */
private void copyRowStyle(XWPFTableRow sourceRow, XWPFTableRow targetRow) {
    // 复制行高等属性
    if (sourceRow.getHeight() > 0) {
        targetRow.setHeight(sourceRow.getHeight());
    }
}

	/**
	 * 安全地清空段落并设置新文本
	 */
	private void clearParagraphAndSetText(XWPFParagraph paragraph, String text) {
		try {
			// 尝试清空现有的runs
			List<XWPFRun> runs = paragraph.getRuns();
			for (int i = runs.size() - 1; i >= 0; i--) {
				paragraph.removeRun(i);
			}
		} catch (Exception e) {
			// 忽略异常，使用替代方法
		}

		// 创建新的run并设置文本
		XWPFRun run = paragraph.createRun();
		run.setText(text);
	}

	/**
	 * 复制单元格样式（包括边框）
	 */
	private void copyCellStyle(XWPFTableCell sourceCell, XWPFTableCell targetCell) {
		try {
			// 复制单元格的CTTc属性
			if (sourceCell.getCTTc() != null && targetCell.getCTTc() != null) {
				// 复制单元格属性
				if (sourceCell.getCTTc().getTcPr() != null) {
					targetCell.getCTTc().setTcPr(sourceCell.getCTTc().getTcPr());
				}
			}
		} catch (Exception e) {
			System.err.println("复制单元格样式失败: " + e.getMessage());
		}
	}

	/**
	 * 使用模板文本填充单元格
	 */
	private void fillCellWithTemplate(XWPFTableCell cell, String templateText, Map<String, Object> data) {
		// 清空单元格
		cell.removeParagraph(0);

		// 创建新段落
		XWPFParagraph paragraph = cell.addParagraph();

		// 替换占位符
		String finalText = templateText;
		if (finalText != null) {
			Pattern pattern = Pattern.compile("\\$\\{([^}]*)\\}");
			Matcher matcher = pattern.matcher(finalText);

			while (matcher.find()) {
				String placeholder = matcher.group(0);
				String key = matcher.group(1);
				Object value = data.get(key);
				String replacement = (value != null) ? value.toString() : "";
				finalText = finalText.replace(placeholder, replacement);
			}

			XWPFRun run = paragraph.createRun();
			run.setText(finalText);
		}
	}

	/**
	 * 复制单元格内容并替换占位符（保留原方法作为备用）
	 */
	private void copyCellContent(XWPFTableCell sourceCell, XWPFTableCell targetCell, Map<String, Object> data) {
		// 清空目标单元格
		if (targetCell.getParagraphs().size() > 0) {
			targetCell.removeParagraph(0);
		}

		// 复制源单元格的段落
		for (XWPFParagraph sourceParagraph : sourceCell.getParagraphs()) {
			XWPFParagraph targetParagraph = targetCell.addParagraph();

			// 复制并替换文本
			String text = sourceParagraph.getText();
			System.out.println("原始单元格文本: " + text);

			if (text != null) {
				// 替换占位符
				Pattern pattern = Pattern.compile("\\$\\{([^}]*)\\}");
				Matcher matcher = pattern.matcher(text);

				while (matcher.find()) {
					String placeholder = matcher.group(0);
					String key = matcher.group(1);
					Object value = data.get(key);
					String replacement = (value != null) ? value.toString() : "";
					text = text.replace(placeholder, replacement);
					System.out.println("替换占位符: " + placeholder + " -> " + replacement);
				}

				XWPFRun run = targetParagraph.createRun();
				run.setText(text);
				System.out.println("最终单元格文本: " + text);
			}
		}
	}
}
